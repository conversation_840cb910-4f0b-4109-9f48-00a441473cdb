@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom Sonner Toast Styling */
/* Make all close buttons use consistent muted color */
.toaster [data-close-button],
.toaster button[data-close-button] {
  /* color: hsl(var(--muted-foreground)) !important; */
  /* background: transparent !important; */
  /* border: none !important; */
  /* opacity: 0.7 !important; */
  /* transition: opacity 0.2s ease !important; */
  width: 20px !important;
  height: 20px !important;
}

.toaster [data-close-button]:hover,
.toaster button[data-close-button]:hover {
  opacity: 1 !important;
  background: hsl(var(--muted) / 0.1) !important;
}

/* Make toast icons bigger and properly colored */
.toaster [data-icon],
.toaster li[data-sonner-toast] > div:first-child {
  width: 20px !important;
  height: 20px !important;
  margin-right: 6px !important;
  margin-left: 2px !important;
  flex-shrink: 0 !important;
}

/* Success toast icon - green */
.toaster li[data-sonner-toast][data-type="success"] [data-icon],
.toaster li[data-sonner-toast][data-type="success"] > div:first-child {
  color: hsl(142.1 76.2% 36.3%) !important; /* green-600 */
}

/* Error toast icon - red */
.toaster li[data-sonner-toast][data-type="error"] [data-icon],
.toaster li[data-sonner-toast][data-type="error"] > div:first-child {
  color: hsl(0 84.2% 60.2%) !important; /* red-500 */
}

/* Warning toast icon - amber */
.toaster li[data-sonner-toast][data-type="warning"] [data-icon],
.toaster li[data-sonner-toast][data-type="warning"] > div:first-child {
  color: hsl(32.1 94.6% 43.7%) !important; /* amber-500 */
}

/* Info toast icon - blue */
.toaster li[data-sonner-toast][data-type="info"] [data-icon],
.toaster li[data-sonner-toast][data-type="info"] > div:first-child {
  color: hsl(221.2 83.2% 53.3%) !important; /* blue-500 */
}

/* Loading toast icon - muted */
.toaster li[data-sonner-toast][data-type="loading"] [data-icon],
.toaster li[data-sonner-toast][data-type="loading"] > div:first-child {
  color: hsl(var(--muted-foreground)) !important;
}

/* Dark mode adjustments */
.dark .toaster li[data-sonner-toast][data-type="success"] [data-icon],
.dark .toaster li[data-sonner-toast][data-type="success"] > div:first-child {
  color: hsl(142.1 70.6% 45.3%) !important; /* green-500 for dark mode */
}

.dark .toaster li[data-sonner-toast][data-type="error"] [data-icon],
.dark .toaster li[data-sonner-toast][data-type="error"] > div:first-child {
  color: hsl(0 72.2% 50.6%) !important; /* red-500 for dark mode */
}

.dark .toaster li[data-sonner-toast][data-type="warning"] [data-icon],
.dark .toaster li[data-sonner-toast][data-type="warning"] > div:first-child {
  color: hsl(32.1 94.6% 43.7%) !important; /* amber-500 for dark mode */
}

.dark .toaster li[data-sonner-toast][data-type="info"] [data-icon],
.dark .toaster li[data-sonner-toast][data-type="info"] > div:first-child {
  color: hsl(217.2 91.2% 59.8%) !important; /* blue-400 for dark mode */
}

/* Ensure toast content has proper spacing with larger icons */
.toaster li[data-sonner-toast] {
  display: flex !important;
  align-items: flex-start !important;
  gap: 8px !important;
  padding: 12px 16px !important;
}

/* Adjust toast title and description spacing */
.toaster [data-title] {
  font-weight: 500 !important;
  line-height: 1.4 !important;
}

.toaster [data-description] {
  opacity: 0.8 !important;
  line-height: 1.4 !important;
  margin-top: 2px !important;
}

/* Ensure consistent toast background and borders */
.toaster li[data-sonner-toast] {
  background: hsl(var(--popover)) !important;
  border: 1px solid hsl(var(--border)) !important;
  color: hsl(var(--popover-foreground)) !important;
}

/* Remove default rich colors background to use our custom styling */
.toaster li[data-sonner-toast][data-type="success"] {
  background: hsl(var(--popover)) !important;
  border-color: hsl(var(--border)) !important;
}

.toaster li[data-sonner-toast][data-type="error"] {
  background: hsl(var(--popover)) !important;
  border-color: hsl(var(--border)) !important;
}

.toaster li[data-sonner-toast][data-type="warning"] {
  background: hsl(var(--popover)) !important;
  border-color: hsl(var(--border)) !important;
}

.toaster li[data-sonner-toast][data-type="info"] {
  background: hsl(var(--popover)) !important;
  border-color: hsl(var(--border)) !important;
}

/* Ensure proper icon sizing for SVG elements */
.toaster li[data-sonner-toast] svg {
  width: 20px !important;
  height: 20px !important;
}
