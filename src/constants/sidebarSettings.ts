import {
  Home,
  Database,
  FileText,
  Users,
  BarChart3,
  Layers,
} from "lucide-react";
import ROUTES from "./routes";

const sidebarSettings = [
  {
    title: "Dashboard",
    url: ROUTES.DASHBOARD,
    icon: Home,
  },
  {
    title: "ERD Designer",
    url: ROUTES.ERD_DESIGNER,
    icon: Database,
  },
  {
    title: "Projects",
    url: ROUTES.PROJECTS,
    icon: Layers,
  },
  {
    title: "Documentation",
    url: ROUTES.DOCUMENTATION,
    icon: FileText,
  },
  {
    title: "Analytics",
    url: ROUTES.ANALYTICS,
    icon: BarChart3,
  },
  {
    title: "Team",
    url: ROUTES.TEAM,
    icon: Users,
  },
];

export default sidebarSettings;
